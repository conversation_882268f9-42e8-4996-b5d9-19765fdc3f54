# Rust 代码可维护性规则

## 1. 代码组织

- [x] 按功能划分模块，使用`mod.rs`明确导出关系
- [x] 避免跨模块循环依赖
- [x] 公共API使用`pub`显式标注
- [x] 私有模块使用`pub(crate)`控制可见性
- [x] 模块深度 ≤ 3 层（避免过度嵌套）
- [x] 单个文件 ≤ 300 行（超限时拆分子模块）
- [x] 禁止 mod.rs 超过 300 行

## 2. 命名规范

- [x] 结构体/枚举使用`UpperCamelCase`
- [x] 函数/方法使用`snake_case`
- [x] 常量使用`SCREAMING_SNAKE_CASE`
- [x] 类型参数使用单字母`T`, `U`, `V`等

## 3. 注释与文档

- [x] 公共API必须包含`///`文档注释
- [x] 使用`# Examples`标注示例代码，并说明输入输出
- [x] 使用`// FIXME:`标记待修复点
- [x] 模块级注释添加`//!`
- [x] 对核心逻辑进行说明，避免重复
- [x] 对入参出参进行详细描述，并且把边界条件和约束要说明，并说明输入输出
- [x] 复杂逻辑添加`// TODO:`标记待完善点
- [x] 模块级注释说明设计意图
- [x] 使用`cargo doc`生成文档

## 4. 错误处理

```rust
// ✅ 使用 thiserror + anyhow 组合
#[derive(Debug, thiserror::Error)]
enum AppError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Parse error")]
    ParseFailed,
}

fn process() -> anyhow::Result<()> {
    let data = std::fs::read("config.toml")?; // 自动转换错误
    // ...
}
```

- [x] 库使用 thiserror 定义结构化错误
- [x] 二进制程序使用 anyhow 进行上下文包装
- [x] 禁止 unwrap()/expect() 生产环境代码
- [x] 错误必须携带上下文：context("Failed to read config")?
- [x] 错误类型必须实现`Error` trait

## 5. 测试规范

```rust
/// 计算两个数的和
/// 
/// # Examples
/// ```
/// assert_eq!(add(2, 3), 5);
/// ```
#[inline]
pub fn add(a: i32, b: i32) -> i32 {
    a + b
}

#[cfg(test)]
mod tests {
    use super::*;
    use proptest::prelude::*;

    #[test]
    fn test_add_basic() {
        assert_eq!(add(2, 3), 5);
    }

    proptest! {
        #[test]
        fn test_add_property(a in -100..100, b in -100..100) {
            assert_eq!(add(a, b), a + b);
        }
    }
}
```

- [x] 单元测试与代码同文件，使用`#[cfg(test)]`
- [x] 集成测试放在`tests/`目录
- [x] 测试用例覆盖所有`Result`分支
- [x] 使用`assert_matches!`验证错误类型
- [x] 使用`proptest`进行属性测试

## 6. 性能优化

- [x] 避免不必要的`clone()`调用
- [x] 使用`Cow`处理借用/拥有数据
- [x] 优先使用迭代器而非显式循环
- [x] 使用`#![deny(clippy::perf)]`启用性能检查

## 7. 安全实践

- [x] 使用`#![deny(unsafe_code)]`限制不安全代码
- [x] 必须使用`unsafe`时单独模块隔离
- [x] 所有`unsafe`块需要详细注释说明
- [x] 使用`#![deny(clippy::security)]`启用安全检查

## 8. 依赖管理

- [x] 使用`cargo clippy`检查依赖安全
- [x] 依赖版本使用`^`限定最小版本
- [x] 开发依赖使用`dev-dependencies`
- [x] 使用`cargo audit`定期检查漏洞

## 9. 格式化标准

- [x] 使用`rustfmt`格式化代码
- [x] 配置`rustfmt.toml`统一格式
- [x] 所有代码提交前执行格式化
- [x] 使用`cargo fmt --all`批量格式化

## 10. 版本控制

- [x] 公共API变更遵循语义化版本
- [x] 使用`cargo clippy`检查API变更
- [x] 重大变更添加`#[deprecated]`标注
- [x] 使用`cargo doc`生成文档变更记录

附录：

1. [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)
2. [Clippy Lints](https://rust-lang.github.io/rust-clippy/master/)
3. [Rustfmt Configuration](https://rust-lang.github.io/rustfmt/)
